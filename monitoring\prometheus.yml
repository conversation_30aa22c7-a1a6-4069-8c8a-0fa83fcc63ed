global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # API Gateway
  - job_name: 'api-gateway'
    static_configs:
      - targets: ['api-gateway:8080']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # Auth Service
  - job_name: 'auth-service'
    static_configs:
      - targets: ['auth-service:3001']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # Link Service
  - job_name: 'link-service'
    static_configs:
      - targets: ['link-service:3002']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # Community Service
  - job_name: 'community-service'
    static_configs:
      - targets: ['community-service:3003']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # Chat Service
  - job_name: 'chat-service'
    static_configs:
      - targets: ['chat-service:3004']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # News Service
  - job_name: 'news-service'
    static_configs:
      - targets: ['news-service:3005']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # Admin Service
  - job_name: 'admin-service'
    static_configs:
      - targets: ['admin-service:3006']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # Redis
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Node Exporter (if added)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s
